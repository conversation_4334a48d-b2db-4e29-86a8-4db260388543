('E:\\compress\\build\\CompressTool-GUI\\PYZ-00.pyz',
 [('__future__', 'D:\\anaconda\\Lib\\__future__.py', 'PYMODULE'),
  ('_colorize', 'D:\\anaconda\\Lib\\_colorize.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\anaconda\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\anaconda\\Lib\\_compression.py', 'PYMODULE'),
  ('_opcode_metadata', 'D:\\anaconda\\Lib\\_opcode_metadata.py', 'PYMODULE'),
  ('_py_abc', 'D:\\anaconda\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\anaconda\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\anaconda\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'D:\\anaconda\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'D:\\anaconda\\Lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'D:\\anaconda\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\anaconda\\Lib\\ast.py', 'PYMODULE'),
  ('base64', 'D:\\anaconda\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'D:\\anaconda\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\anaconda\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\anaconda\\Lib\\calendar.py', 'PYMODULE'),
  ('compress_tool', 'E:\\compress\\compress_tool\\__init__.py', 'PYMODULE'),
  ('compress_tool.base', 'E:\\compress\\compress_tool\\base.py', 'PYMODULE'),
  ('compress_tool.core', 'E:\\compress\\compress_tool\\core.py', 'PYMODULE'),
  ('compress_tool.formats',
   'E:\\compress\\compress_tool\\formats\\__init__.py',
   'PYMODULE'),
  ('compress_tool.formats.rar_format',
   'E:\\compress\\compress_tool\\formats\\rar_format.py',
   'PYMODULE'),
  ('compress_tool.formats.seven_zip',
   'E:\\compress\\compress_tool\\formats\\seven_zip.py',
   'PYMODULE'),
  ('compress_tool.formats.tar_format',
   'E:\\compress\\compress_tool\\formats\\tar_format.py',
   'PYMODULE'),
  ('compress_tool.formats.zip_format',
   'E:\\compress\\compress_tool\\formats\\zip_format.py',
   'PYMODULE'),
  ('compress_tool.gui', 'E:\\compress\\compress_tool\\gui.py', 'PYMODULE'),
  ('contextlib', 'D:\\anaconda\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\anaconda\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\anaconda\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\anaconda\\Lib\\csv.py', 'PYMODULE'),
  ('dataclasses', 'D:\\anaconda\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\anaconda\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\anaconda\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'D:\\anaconda\\Lib\\dis.py', 'PYMODULE'),
  ('email', 'D:\\anaconda\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\anaconda\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\anaconda\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'D:\\anaconda\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase', 'D:\\anaconda\\Lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\anaconda\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'D:\\anaconda\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\anaconda\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\anaconda\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\anaconda\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\anaconda\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'D:\\anaconda\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'D:\\anaconda\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\anaconda\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\anaconda\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'D:\\anaconda\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\anaconda\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\anaconda\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\anaconda\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'D:\\anaconda\\Lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'D:\\anaconda\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\anaconda\\Lib\\fractions.py', 'PYMODULE'),
  ('getopt', 'D:\\anaconda\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'D:\\anaconda\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\anaconda\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\anaconda\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\anaconda\\Lib\\hashlib.py', 'PYMODULE'),
  ('importlib', 'D:\\anaconda\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\anaconda\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\anaconda\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\anaconda\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\anaconda\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\anaconda\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\anaconda\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\anaconda\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\anaconda\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\anaconda\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\anaconda\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\anaconda\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\anaconda\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers', 'D:\\anaconda\\Lib\\importlib\\readers.py', 'PYMODULE'),
  ('importlib.resources',
   'D:\\anaconda\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\anaconda\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\anaconda\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'D:\\anaconda\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\anaconda\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\anaconda\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\anaconda\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\anaconda\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'D:\\anaconda\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\anaconda\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'D:\\anaconda\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'D:\\anaconda\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'D:\\anaconda\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\anaconda\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'D:\\anaconda\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'D:\\anaconda\\Lib\\lzma.py', 'PYMODULE'),
  ('numbers', 'D:\\anaconda\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\anaconda\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib', 'D:\\anaconda\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._abc', 'D:\\anaconda\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('pathlib._local', 'D:\\anaconda\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('pickle', 'D:\\anaconda\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\anaconda\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\anaconda\\Lib\\py_compile.py', 'PYMODULE'),
  ('quopri', 'D:\\anaconda\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\anaconda\\Lib\\random.py', 'PYMODULE'),
  ('selectors', 'D:\\anaconda\\Lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'D:\\anaconda\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\anaconda\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'D:\\anaconda\\Lib\\socket.py', 'PYMODULE'),
  ('statistics', 'D:\\anaconda\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\anaconda\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\anaconda\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\anaconda\\Lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'D:\\anaconda\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\anaconda\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\anaconda\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\anaconda\\Lib\\threading.py', 'PYMODULE'),
  ('tkinter', 'D:\\anaconda\\Lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\anaconda\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants', 'D:\\anaconda\\Lib\\tkinter\\constants.py', 'PYMODULE'),
  ('tkinter.dialog', 'D:\\anaconda\\Lib\\tkinter\\dialog.py', 'PYMODULE'),
  ('tkinter.filedialog',
   'D:\\anaconda\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'D:\\anaconda\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\anaconda\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk', 'D:\\anaconda\\Lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('token', 'D:\\anaconda\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\anaconda\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\anaconda\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'D:\\anaconda\\Lib\\typing.py', 'PYMODULE'),
  ('urllib', 'D:\\anaconda\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\anaconda\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('zipfile', 'D:\\anaconda\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'D:\\anaconda\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\anaconda\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE')])
