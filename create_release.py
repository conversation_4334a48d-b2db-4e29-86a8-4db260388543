#!/usr/bin/env python3
"""
创建发布包脚本
"""

import os
import shutil
import zipfile
from pathlib import Path
from datetime import datetime

# 项目信息
PROJECT_NAME = "CompressTool"
VERSION = "1.0.0"
RELEASE_DATE = datetime.now().strftime("%Y-%m-%d")

def create_release_package():
    """创建发布包"""
    print(f"创建 {PROJECT_NAME} v{VERSION} 发布包...")
    
    # 创建发布目录
    release_dir = f"{PROJECT_NAME}-v{VERSION}"
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    os.makedirs(release_dir)
    
    # 复制可执行文件
    if os.path.exists("dist/CompressTool-CLI.exe"):
        shutil.copy2("dist/CompressTool-CLI.exe", release_dir)
        print("✓ 复制 CLI 可执行文件")
    
    if os.path.exists("dist/CompressTool-GUI.exe"):
        shutil.copy2("dist/CompressTool-GUI.exe", release_dir)
        print("✓ 复制 GUI 可执行文件")
    
    # 创建示例数据目录
    samples_dir = os.path.join(release_dir, "samples")
    os.makedirs(samples_dir)
    
    if os.path.exists("test_data"):
        shutil.copytree("test_data", os.path.join(samples_dir, "test_data"))
        print("✓ 复制示例数据")
    
    # 创建文档
    create_documentation(release_dir)
    
    # 创建批处理文件
    create_batch_files(release_dir)
    
    # 创建ZIP包
    create_zip_package(release_dir)
    
    print(f"\n✓ 发布包创建完成: {release_dir}")
    return release_dir

def create_documentation(release_dir):
    """创建文档"""
    
    # 使用说明
    readme_content = f"""# {PROJECT_NAME} v{VERSION}

## 简介
{PROJECT_NAME} 是一个功能强大的压缩解压工具，支持ZIP、RAR、TAR、7Z等主流压缩格式。

## 版本信息
- 版本: {VERSION}
- 发布日期: {RELEASE_DATE}
- 支持系统: Windows 10/11

## 文件说明
- `CompressTool-CLI.exe` - 命令行版本
- `CompressTool-GUI.exe` - 图形界面版本
- `samples/` - 示例文件
- `使用说明.txt` - 详细使用说明
- `快速开始.bat` - 快速开始脚本

## 快速开始

### 图形界面版本
双击 `CompressTool-GUI.exe` 启动图形界面。

### 命令行版本
打开命令提示符，运行：
```
CompressTool-CLI.exe --help
```

## 支持的格式

### 压缩格式
- ZIP (.zip)
- TAR (.tar, .tar.gz, .tar.bz2, .tar.xz)
- 7Z (.7z) *需要额外库支持

### 解压格式
- 所有压缩格式
- RAR (.rar) *需要额外库支持

## 常用命令

### 压缩文件
```
CompressTool-CLI.exe compress file.txt output.zip
CompressTool-CLI.exe compress folder/ output.tar.gz
```

### 解压文件
```
CompressTool-CLI.exe extract archive.zip
CompressTool-CLI.exe extract archive.zip target_folder/
```

### 查看内容
```
CompressTool-CLI.exe list archive.zip
CompressTool-CLI.exe list archive.zip --detailed
```

## 注意事项
1. 首次运行可能需要Windows Defender确认
2. 大文件处理时请耐心等待
3. RAR和7Z格式需要额外的库支持

## 技术支持
如有问题，请检查：
- 文件路径是否正确
- 文件是否被占用
- 磁盘空间是否充足

更多信息请参考详细使用说明。
"""
    
    readme_file = os.path.join(release_dir, "README.txt")
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    # 详细使用说明
    usage_content = f"""# {PROJECT_NAME} 详细使用说明

## 命令行界面详细说明

### 基本语法
CompressTool-CLI.exe [OPTIONS] COMMAND [ARGS]...

### 全局选项
--version    显示版本信息
--help       显示帮助信息

### 命令详解

#### 1. compress - 压缩文件
语法: CompressTool-CLI.exe compress [OPTIONS] SOURCES... OUTPUT

选项:
-l, --level INTEGER    压缩级别 (0-9, 默认6)
-p, --password TEXT    密码保护
-f, --format TEXT      强制指定格式
-v, --verbose          详细输出

示例:
CompressTool-CLI.exe compress file.txt output.zip
CompressTool-CLI.exe compress -l 9 file.txt output.zip
CompressTool-CLI.exe compress -p mypass file.txt output.zip
CompressTool-CLI.exe compress file1.txt file2.txt output.zip
CompressTool-CLI.exe compress folder/ output.tar.gz

#### 2. extract - 解压文件
语法: CompressTool-CLI.exe extract [OPTIONS] ARCHIVE [DESTINATION]

选项:
-p, --password TEXT    密码
-f, --files TEXT       指定解压的文件 (可多次使用)
-v, --verbose          详细输出

示例:
CompressTool-CLI.exe extract archive.zip
CompressTool-CLI.exe extract archive.zip target/
CompressTool-CLI.exe extract -p mypass archive.zip
CompressTool-CLI.exe extract -f file1.txt -f file2.txt archive.zip

#### 3. list - 列出内容
语法: CompressTool-CLI.exe list [OPTIONS] ARCHIVE

选项:
-p, --password TEXT    密码
-d, --detailed         显示详细信息

示例:
CompressTool-CLI.exe list archive.zip
CompressTool-CLI.exe list -d archive.zip
CompressTool-CLI.exe list -p mypass archive.zip

#### 4. formats - 显示支持格式
语法: CompressTool-CLI.exe formats

## 图形界面使用说明

### 主界面
1. 操作选择: 选择压缩、解压或查看内容
2. 文件选择: 选择源文件或压缩文件
3. 目标设置: 设置输出路径
4. 选项配置: 设置压缩级别、密码等
5. 执行操作: 点击按钮开始操作

### 操作步骤
1. 启动程序: 双击 CompressTool-GUI.exe
2. 选择操作类型
3. 点击"浏览"选择文件
4. 设置输出路径
5. 配置选项（可选）
6. 点击执行按钮
7. 查看结果

### 拖拽支持
可以直接将文件拖拽到界面中。

## 高级功能

### 批量操作
命令行版本支持批量处理多个文件：
CompressTool-CLI.exe compress *.txt output.zip

### 压缩级别
- 0: 不压缩（最快）
- 1-3: 低压缩（较快）
- 4-6: 中等压缩（平衡）
- 7-9: 高压缩（较慢）

### 密码保护
支持为ZIP文件设置密码保护。

### 格式转换
可以通过解压再压缩实现格式转换。

## 故障排除

### 常见问题
1. 程序无法启动
   - 检查Windows版本兼容性
   - 确认杀毒软件未阻止

2. 压缩失败
   - 检查源文件是否存在
   - 确认目标路径可写
   - 检查磁盘空间

3. 解压失败
   - 检查压缩文件是否完整
   - 确认密码正确
   - 检查目标路径权限

4. RAR/7Z不支持
   - 这些格式需要额外库支持
   - 可以解压但不能压缩RAR

### 错误代码
- 0: 成功
- 1: 一般错误
- 2: 文件不存在
- 3: 权限不足

## 性能优化

### 大文件处理
- 使用较低的压缩级别
- 确保足够的磁盘空间
- 避免在网络驱动器上操作

### 内存使用
- 程序会根据文件大小自动调整
- 超大文件可能需要更多内存

## 更新日志

### v{VERSION} ({RELEASE_DATE})
- 初始版本发布
- 支持ZIP、TAR、RAR、7Z格式
- 提供CLI和GUI界面
- 完整的功能测试

## 许可证
本软件遵循MIT许可证。

## 联系方式
如有问题或建议，请通过项目页面联系。
"""
    
    usage_file = os.path.join(release_dir, "使用说明.txt")
    with open(usage_file, 'w', encoding='utf-8') as f:
        f.write(usage_content)
    
    print("✓ 创建文档")

def create_batch_files(release_dir):
    """创建批处理文件"""
    
    # 快速开始脚本
    quick_start = """@echo off
echo ========================================
echo     压缩解压工具 快速开始
echo ========================================
echo.
echo 选择操作:
echo 1. 启动图形界面
echo 2. 查看命令行帮助
echo 3. 显示支持格式
echo 4. 压缩示例文件
echo 5. 退出
echo.
set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" (
    echo 启动图形界面...
    start CompressTool-GUI.exe
    goto end
)

if "%choice%"=="2" (
    echo.
    CompressTool-CLI.exe --help
    pause
    goto end
)

if "%choice%"=="3" (
    echo.
    CompressTool-CLI.exe formats
    pause
    goto end
)

if "%choice%"=="4" (
    echo.
    echo 压缩示例文件...
    if exist samples\\test_data (
        CompressTool-CLI.exe compress samples\\test_data sample_compressed.zip -v
        echo.
        echo 压缩完成！生成文件: sample_compressed.zip
    ) else (
        echo 示例文件不存在！
    )
    pause
    goto end
)

if "%choice%"=="5" (
    goto end
)

echo 无效选择！
pause

:end
"""
    
    batch_file = os.path.join(release_dir, "快速开始.bat")
    with open(batch_file, 'w', encoding='gbk') as f:
        f.write(quick_start)
    
    # 命令行快捷方式
    cli_shortcut = """@echo off
echo 压缩解压工具 - 命令行模式
echo 输入 'help' 查看帮助，输入 'exit' 退出
echo.

:loop
set /p cmd=CompressTool^> 

if "%cmd%"=="exit" goto end
if "%cmd%"=="help" (
    CompressTool-CLI.exe --help
    echo.
    goto loop
)

CompressTool-CLI.exe %cmd%
echo.
goto loop

:end
"""
    
    cli_file = os.path.join(release_dir, "命令行模式.bat")
    with open(cli_file, 'w', encoding='gbk') as f:
        f.write(cli_shortcut)
    
    print("✓ 创建批处理文件")

def create_zip_package(release_dir):
    """创建ZIP发布包"""
    zip_name = f"{release_dir}.zip"
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(release_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_path = os.path.relpath(file_path, os.path.dirname(release_dir))
                zipf.write(file_path, arc_path)
    
    zip_size = os.path.getsize(zip_name)
    print(f"✓ 创建ZIP包: {zip_name} ({zip_size:,} 字节)")

def main():
    """主函数"""
    print("=" * 50)
    print("创建发布包")
    print("=" * 50)
    
    if not os.path.exists("dist"):
        print("错误: dist目录不存在，请先构建可执行文件")
        return False
    
    release_dir = create_release_package()
    
    print("\n" + "=" * 50)
    print("发布包创建完成！")
    print("=" * 50)
    print(f"发布目录: {release_dir}/")
    print(f"ZIP包: {release_dir}.zip")
    print("\n包含文件:")
    
    for root, dirs, files in os.walk(release_dir):
        level = root.replace(release_dir, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            file_path = os.path.join(root, file)
            size = os.path.getsize(file_path)
            print(f"{subindent}{file} ({size:,} 字节)")
    
    return True

if __name__ == '__main__':
    success = main()
    if success:
        print("\n发布包已准备就绪！")
    else:
        print("\n发布包创建失败！")
