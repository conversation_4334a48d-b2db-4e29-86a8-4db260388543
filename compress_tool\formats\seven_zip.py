"""
7Z格式压缩解压实现
"""

import os
from pathlib import Path
from typing import List, Optional, Union
from ..base import BaseCompressor, CompressionError

try:
    import py7zr
    PY7ZR_AVAILABLE = True
except ImportError:
    PY7ZR_AVAILABLE = False


class SevenZipCompressor(BaseCompressor):
    """7Z格式压缩器"""
    
    def __init__(self):
        super().__init__()
        self.supported_extensions = ['.7z']
        
        if not PY7ZR_AVAILABLE:
            print("警告: py7zr库未安装，7Z支持不可用")
            print("请运行: pip install py7zr")
    
    def compress(self, source_paths: Union[str, List[str]], 
                 output_path: str, 
                 compression_level: int = 6,
                 password: Optional[str] = None) -> bool:
        """压缩为7Z格式"""
        if not PY7ZR_AVAILABLE:
            raise CompressionError("py7zr库未安装，无法处理7Z文件")
        
        try:
            if not self.validate_paths(output_path):
                raise CompressionError("输出路径无效")
            
            if isinstance(source_paths, str):
                source_paths = [source_paths]
            
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir:  # 只有当目录不为空时才创建
                os.makedirs(output_dir, exist_ok=True)
            
            with py7zr.SevenZipFile(output_path, 'w', password=password) as archive:
                for source_path in source_paths:
                    source_path = Path(source_path)
                    
                    if not source_path.exists():
                        raise CompressionError(f"源路径不存在: {source_path}")
                    
                    if source_path.is_file():
                        archive.write(source_path, source_path.name)
                    elif source_path.is_dir():
                        # 递归添加目录
                        for item in source_path.rglob('*'):
                            if item.is_file():
                                arcname = item.relative_to(source_path.parent)
                                archive.write(item, arcname)
            
            return True
            
        except Exception as e:
            raise CompressionError(f"7Z压缩失败: {str(e)}")
    
    def extract(self, archive_path: str, 
                extract_to: str,
                password: Optional[str] = None,
                members: Optional[List[str]] = None) -> bool:
        """解压7Z文件"""
        if not PY7ZR_AVAILABLE:
            raise CompressionError("py7zr库未安装，无法处理7Z文件")
        
        try:
            if not self.validate_paths(archive_path, extract_to):
                raise CompressionError("路径无效")
            
            if not os.path.exists(archive_path):
                raise CompressionError(f"压缩文件不存在: {archive_path}")
            
            os.makedirs(extract_to, exist_ok=True)
            
            with py7zr.SevenZipFile(archive_path, mode='r', password=password) as archive:
                if members:
                    # 提取指定文件
                    archive.extract(path=extract_to, targets=members)
                else:
                    # 提取所有文件
                    archive.extractall(path=extract_to)
            
            return True
            
        except py7zr.Bad7zFile:
            raise CompressionError("无效的7Z文件格式")
        except Exception as e:
            raise CompressionError(f"7Z解压失败: {str(e)}")
    
    def list_contents(self, archive_path: str, 
                     password: Optional[str] = None) -> List[dict]:
        """列出7Z文件内容"""
        if not PY7ZR_AVAILABLE:
            raise CompressionError("py7zr库未安装，无法处理7Z文件")
        
        try:
            if not os.path.exists(archive_path):
                raise CompressionError(f"压缩文件不存在: {archive_path}")
            
            contents = []
            with py7zr.SevenZipFile(archive_path, mode='r', password=password) as archive:
                for info in archive.list():
                    contents.append({
                        'filename': info.filename,
                        'file_size': info.uncompressed if hasattr(info, 'uncompressed') else 0,
                        'compress_size': info.compressed if hasattr(info, 'compressed') else 0,
                        'date_time': info.creationtime if hasattr(info, 'creationtime') else None,
                        'is_dir': info.is_directory if hasattr(info, 'is_directory') else False,
                        'crc': info.crc32 if hasattr(info, 'crc32') else None
                    })
            
            return contents
            
        except py7zr.Bad7zFile:
            raise CompressionError("无效的7Z文件格式")
        except Exception as e:
            raise CompressionError(f"读取7Z文件内容失败: {str(e)}")
    
    def test_archive(self, archive_path: str, password: Optional[str] = None) -> bool:
        """测试7Z文件完整性"""
        if not PY7ZR_AVAILABLE:
            raise CompressionError("py7zr库未安装，无法处理7Z文件")
        
        try:
            with py7zr.SevenZipFile(archive_path, mode='r', password=password) as archive:
                return archive.testzip() is None
                
        except Exception as e:
            raise CompressionError(f"7Z文件测试失败: {str(e)}")
