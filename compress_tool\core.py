"""
压缩解压核心管理类
"""

import os
from pathlib import Path
from typing import List, Optional, Union, Dict, Any
from .base import BaseCompressor, CompressionError
from .formats import ZipCompressor, TarCompressor, RarCompressor, SevenZipCompressor


class CompressManager:
    """压缩解压管理器"""
    
    def __init__(self):
        self.compressors: Dict[str, BaseCompressor] = {}
        self._register_compressors()
    
    def _register_compressors(self):
        """注册所有压缩器"""
        compressors = [
            ZipCompressor(),
            TarCompressor(),
            RarCompressor(),
            SevenZipCompressor()
        ]
        
        for compressor in compressors:
            for ext in compressor.supported_extensions:
                self.compressors[ext] = compressor
    
    def get_supported_formats(self) -> Dict[str, List[str]]:
        """获取支持的格式列表"""
        formats = {
            'compress': [],
            'extract': []
        }
        
        for ext, compressor in self.compressors.items():
            formats['extract'].append(ext)
            # RAR只支持解压
            if not isinstance(compressor, RarCompressor):
                formats['compress'].append(ext)
        
        return formats
    
    def detect_format(self, file_path: str) -> Optional[str]:
        """检测文件格式"""
        file_path = file_path.lower()

        # 检查复合扩展名 (如 .tar.gz)
        for ext in ['.tar.gz', '.tar.bz2', '.tar.xz', '.tgz', '.tbz2', '.txz']:
            if file_path.endswith(ext):
                return ext

        # 检查单一扩展名
        ext = Path(file_path).suffix
        return ext if ext in self.compressors else None
    
    def compress(self, source_paths: Union[str, List[str]], 
                 output_path: str, 
                 compression_level: int = 6,
                 password: Optional[str] = None,
                 format_type: Optional[str] = None) -> bool:
        """
        压缩文件或文件夹
        
        Args:
            source_paths: 源文件或文件夹路径
            output_path: 输出压缩文件路径
            compression_level: 压缩级别 (0-9)
            password: 密码保护 (可选)
            format_type: 强制指定格式 (可选)
            
        Returns:
            bool: 压缩是否成功
        """
        try:
            # 确定格式
            if format_type:
                ext = format_type if format_type.startswith('.') else f'.{format_type}'
            else:
                ext = self.detect_format(output_path)
            
            if not ext or ext not in self.compressors:
                raise CompressionError(f"不支持的压缩格式: {ext}")
            
            compressor = self.compressors[ext]
            
            # 检查是否支持压缩
            if isinstance(compressor, RarCompressor):
                raise CompressionError("RAR格式不支持压缩操作")
            
            return compressor.compress(source_paths, output_path, 
                                     compression_level, password)
            
        except Exception as e:
            raise CompressionError(f"压缩操作失败: {str(e)}")
    
    def extract(self, archive_path: str, 
                extract_to: str,
                password: Optional[str] = None,
                members: Optional[List[str]] = None) -> bool:
        """
        解压文件
        
        Args:
            archive_path: 压缩文件路径
            extract_to: 解压目标目录
            password: 密码 (可选)
            members: 指定解压的文件列表 (可选)
            
        Returns:
            bool: 解压是否成功
        """
        try:
            ext = self.detect_format(archive_path)
            
            if not ext or ext not in self.compressors:
                raise CompressionError(f"不支持的压缩格式: {ext}")
            
            compressor = self.compressors[ext]
            return compressor.extract(archive_path, extract_to, password, members)
            
        except Exception as e:
            raise CompressionError(f"解压操作失败: {str(e)}")
    
    def list_contents(self, archive_path: str, 
                     password: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        列出压缩文件内容
        
        Args:
            archive_path: 压缩文件路径
            password: 密码 (可选)
            
        Returns:
            List[Dict]: 文件信息列表
        """
        try:
            ext = self.detect_format(archive_path)
            
            if not ext or ext not in self.compressors:
                raise CompressionError(f"不支持的压缩格式: {ext}")
            
            compressor = self.compressors[ext]
            return compressor.list_contents(archive_path, password)
            
        except Exception as e:
            raise CompressionError(f"读取压缩文件内容失败: {str(e)}")
    
    def get_archive_info(self, archive_path: str, 
                        password: Optional[str] = None) -> Dict[str, Any]:
        """获取压缩文件详细信息"""
        try:
            if not os.path.exists(archive_path):
                raise CompressionError(f"文件不存在: {archive_path}")
            
            contents = self.list_contents(archive_path, password)
            
            total_files = len([c for c in contents if not c.get('is_dir', False)])
            total_dirs = len([c for c in contents if c.get('is_dir', False)])
            total_size = sum(c.get('file_size', 0) for c in contents)
            compressed_size = sum(c.get('compress_size', 0) for c in contents)
            
            return {
                'archive_path': archive_path,
                'format': self.detect_format(archive_path),
                'file_size': os.path.getsize(archive_path),
                'total_files': total_files,
                'total_directories': total_dirs,
                'uncompressed_size': total_size,
                'compressed_size': compressed_size,
                'compression_ratio': (1 - compressed_size / total_size) * 100 if total_size > 0 else 0,
                'contents': contents
            }
            
        except Exception as e:
            raise CompressionError(f"获取压缩文件信息失败: {str(e)}")
