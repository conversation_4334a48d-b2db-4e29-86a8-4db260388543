"""
ZIP格式压缩解压实现
"""

import zipfile
import os
from pathlib import Path
from typing import List, Optional, Union
from ..base import BaseCompressor, CompressionError


class ZipCompressor(BaseCompressor):
    """ZIP格式压缩器"""
    
    def __init__(self):
        super().__init__()
        self.supported_extensions = ['.zip']
    
    def compress(self, source_paths: Union[str, List[str]], 
                 output_path: str, 
                 compression_level: int = 6,
                 password: Optional[str] = None) -> bool:
        """压缩为ZIP格式"""
        try:
            if not self.validate_paths(output_path):
                raise CompressionError("输出路径无效")
            
            if isinstance(source_paths, str):
                source_paths = [source_paths]
            
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir:  # 只有当目录不为空时才创建
                os.makedirs(output_dir, exist_ok=True)
            
            with zipfile.ZipFile(output_path, 'w', 
                               compression=zipfile.ZIP_DEFLATED,
                               compresslevel=compression_level) as zipf:
                
                for source_path in source_paths:
                    source_path = Path(source_path)
                    
                    if not source_path.exists():
                        raise CompressionError(f"源路径不存在: {source_path}")
                    
                    if source_path.is_file():
                        zipf.write(source_path, source_path.name)
                    elif source_path.is_dir():
                        self._add_directory_to_zip(zipf, source_path)
                
                # 如果设置了密码，需要使用pyminizip或其他库
                if password:
                    print("警告: 标准zipfile库不支持密码保护，建议使用pyminizip")
            
            return True
            
        except Exception as e:
            raise CompressionError(f"ZIP压缩失败: {str(e)}")
    
    def extract(self, archive_path: str, 
                extract_to: str,
                password: Optional[str] = None,
                members: Optional[List[str]] = None) -> bool:
        """解压ZIP文件"""
        try:
            if not self.validate_paths(archive_path, extract_to):
                raise CompressionError("路径无效")
            
            if not os.path.exists(archive_path):
                raise CompressionError(f"压缩文件不存在: {archive_path}")
            
            os.makedirs(extract_to, exist_ok=True)
            
            with zipfile.ZipFile(archive_path, 'r') as zipf:
                if password:
                    zipf.setpassword(password.encode())
                
                if members:
                    for member in members:
                        zipf.extract(member, extract_to)
                else:
                    zipf.extractall(extract_to)
            
            return True
            
        except Exception as e:
            raise CompressionError(f"ZIP解压失败: {str(e)}")
    
    def list_contents(self, archive_path: str, 
                     password: Optional[str] = None) -> List[dict]:
        """列出ZIP文件内容"""
        try:
            if not os.path.exists(archive_path):
                raise CompressionError(f"压缩文件不存在: {archive_path}")
            
            contents = []
            with zipfile.ZipFile(archive_path, 'r') as zipf:
                if password:
                    zipf.setpassword(password.encode())
                
                for info in zipf.infolist():
                    contents.append({
                        'filename': info.filename,
                        'file_size': info.file_size,
                        'compress_size': info.compress_size,
                        'date_time': info.date_time,
                        'is_dir': info.is_dir(),
                        'crc': info.CRC
                    })
            
            return contents
            
        except Exception as e:
            raise CompressionError(f"读取ZIP文件内容失败: {str(e)}")
    
    def _add_directory_to_zip(self, zipf: zipfile.ZipFile, dir_path: Path, base_path: Optional[Path] = None):
        """递归添加目录到ZIP文件"""
        if base_path is None:
            base_path = dir_path.parent
        
        for item in dir_path.rglob('*'):
            if item.is_file():
                arcname = item.relative_to(base_path)
                zipf.write(item, arcname)
