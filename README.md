# 压缩解压工具

一个功能强大的压缩解压工具，支持ZIP、RAR、TAR、7Z等主流压缩格式。提供命令行界面和图形用户界面两种使用方式。

## 功能特性

- ✅ **多格式支持**: ZIP、RAR、TAR、TAR.GZ、TAR.BZ2、TAR.XZ、7Z
- ✅ **压缩功能**: 支持ZIP、TAR系列、7Z格式的压缩
- ✅ **解压功能**: 支持所有主流格式的解压
- ✅ **密码保护**: 支持密码保护的压缩和解压
- ✅ **批量操作**: 支持多文件和目录的批量处理
- ✅ **进度显示**: 实时显示操作进度
- ✅ **命令行界面**: 适合脚本和自动化使用
- ✅ **图形界面**: 用户友好的GUI界面
- ✅ **文件列表**: 查看压缩文件内容而不解压

## 支持的格式

### 压缩格式
- ZIP (.zip)
- TAR (.tar)
- TAR.GZ (.tar.gz, .tgz)
- TAR.BZ2 (.tar.bz2, .tbz2)
- TAR.XZ (.tar.xz, .txz)
- 7Z (.7z)

### 解压格式
- 所有压缩格式 + RAR (.rar)

*注意: RAR是专有格式，只支持解压，不支持压缩*

## 安装

### 1. 克隆项目
```bash
git clone <repository-url>
cd compress
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 安装可选依赖
```bash
# 用于RAR支持
pip install rarfile

# 用于7Z支持  
pip install py7zr

# 用于高级压缩功能
pip install patool
```

## 使用方法

### 命令行界面

#### 基本用法
```bash
# 显示帮助
python main.py --help

# 查看支持的格式
python main.py formats

# 压缩文件
python main.py compress file1.txt file2.txt output.zip

# 压缩文件夹
python main.py compress folder/ output.tar.gz

# 解压文件
python main.py extract archive.zip

# 解压到指定目录
python main.py extract archive.zip /path/to/extract

# 查看压缩文件内容
python main.py list archive.zip

# 查看详细信息
python main.py list archive.zip --detailed
```

#### 高级选项
```bash
# 设置压缩级别 (0-9)
python main.py compress -l 9 file.txt output.zip

# 密码保护
python main.py compress -p mypassword file.txt output.zip
python main.py extract -p mypassword archive.zip

# 强制指定格式
python main.py compress -f zip file.txt output.archive

# 详细输出
python main.py compress -v file.txt output.zip

# 解压指定文件
python main.py extract archive.zip -f file1.txt -f file2.txt
```

### 图形用户界面

启动GUI界面:
```bash
python gui.py
```

GUI功能:
- 拖拽文件到界面
- 选择压缩/解压/查看操作
- 设置压缩级别和密码
- 实时进度显示
- 结果查看

### Python API

```python
from compress_tool import CompressManager

# 创建管理器
manager = CompressManager()

# 压缩文件
success = manager.compress(
    source_paths=['file1.txt', 'file2.txt'],
    output_path='output.zip',
    compression_level=6,
    password='mypassword'
)

# 解压文件
success = manager.extract(
    archive_path='archive.zip',
    extract_to='./extracted/',
    password='mypassword'
)

# 列出内容
contents = manager.list_contents('archive.zip')
for item in contents:
    print(f"{item['filename']} - {item['file_size']} bytes")

# 获取详细信息
info = manager.get_archive_info('archive.zip')
print(f"压缩率: {info['compression_ratio']:.1f}%")
```

## 项目结构

```
compress/
├── compress_tool/          # 主要代码包
│   ├── __init__.py        # 包初始化
│   ├── base.py            # 基础接口定义
│   ├── core.py            # 核心管理类
│   ├── cli.py             # 命令行界面
│   ├── gui.py             # 图形用户界面
│   └── formats/           # 格式处理器
│       ├── __init__.py
│       ├── zip_format.py  # ZIP格式
│       ├── tar_format.py  # TAR格式
│       ├── rar_format.py  # RAR格式
│       └── seven_zip.py   # 7Z格式
├── tests/                 # 测试代码
│   ├── __init__.py
│   └── test_compress.py   # 主要测试
├── main.py               # 命令行入口
├── gui.py                # GUI入口
├── run_tests.py          # 测试运行器
├── requirements.txt      # 依赖列表
└── README.md            # 项目文档
```

## 测试

运行所有测试:
```bash
python run_tests.py
```

运行特定测试:
```bash
python -m unittest tests.test_compress.TestCompressManager.test_zip_compress_extract
```

## 依赖说明

### 核心依赖
- `click`: 命令行界面框架
- `tqdm`: 进度条显示
- `tkinter`: GUI界面 (Python标准库)

### 可选依赖
- `rarfile`: RAR格式支持
- `py7zr`: 7Z格式支持
- `patool`: 通用压缩工具包装器

## 常见问题

### Q: RAR文件无法处理？
A: 请安装rarfile库: `pip install rarfile`

### Q: 7Z文件无法处理？
A: 请安装py7zr库: `pip install py7zr`

### Q: 压缩时提示格式不支持？
A: 检查文件扩展名是否正确，或使用`-f`参数强制指定格式

### Q: 密码保护的文件无法解压？
A: 确保使用`-p`参数提供正确的密码

### Q: GUI界面无法启动？
A: 确保系统支持tkinter，在某些Linux发行版中需要单独安装

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持ZIP、TAR、RAR、7Z格式
- 提供CLI和GUI界面
- 完整的测试覆盖
