# CompressTool v1.0.0 发布说明

## 🎉 首次发布

我们很高兴地宣布 CompressTool v1.0.0 正式发布！这是一个功能强大的压缩解压工具，支持多种主流压缩格式。

## 📦 下载

### 完整发布包
- **CompressTool-v1.0.0.zip** (约 134 MB)
  - 包含CLI和GUI两个版本
  - 包含示例文件和完整文档
  - 包含快速开始脚本

### 单独下载
- **CompressTool-CLI.exe** (约 124 MB) - 命令行版本
- **CompressTool-GUI.exe** (约 11 MB) - 图形界面版本

## ✨ 主要特性

### 🗜️ 压缩格式支持
- ✅ ZIP (.zip) - 完全支持
- ✅ TAR (.tar, .tar.gz, .tar.bz2, .tar.xz) - 完全支持
- ⚠️ 7Z (.7z) - 需要额外库支持
- ⚠️ RAR (.rar) - 仅支持解压

### 🖥️ 用户界面
- **命令行界面 (CLI)**: 适合自动化和脚本使用
- **图形界面 (GUI)**: 用户友好的可视化操作

### 🔧 核心功能
- 文件和文件夹压缩
- 压缩文件解压
- 查看压缩文件内容
- 密码保护支持
- 多种压缩级别
- 批量操作支持
- 进度显示

## 🚀 快速开始

### 方法一：使用图形界面
1. 下载并解压 `CompressTool-v1.0.0.zip`
2. 双击 `CompressTool-GUI.exe`
3. 选择操作类型，选择文件，点击执行

### 方法二：使用命令行
1. 下载并解压 `CompressTool-v1.0.0.zip`
2. 打开命令提示符，导航到解压目录
3. 运行命令：
   ```
   CompressTool-CLI.exe --help
   ```

### 方法三：使用快速开始脚本
1. 下载并解压 `CompressTool-v1.0.0.zip`
2. 双击 `快速开始.bat`
3. 按提示选择操作

## 📖 使用示例

### 压缩文件
```bash
# 压缩单个文件
CompressTool-CLI.exe compress document.txt archive.zip

# 压缩多个文件
CompressTool-CLI.exe compress file1.txt file2.txt archive.zip

# 压缩文件夹
CompressTool-CLI.exe compress my_folder/ archive.tar.gz

# 高压缩级别
CompressTool-CLI.exe compress -l 9 large_file.txt compressed.zip

# 密码保护
CompressTool-CLI.exe compress -p mypassword secret.txt protected.zip
```

### 解压文件
```bash
# 解压到当前目录
CompressTool-CLI.exe extract archive.zip

# 解压到指定目录
CompressTool-CLI.exe extract archive.zip extracted_files/

# 密码保护的文件
CompressTool-CLI.exe extract -p mypassword protected.zip
```

### 查看内容
```bash
# 简单列表
CompressTool-CLI.exe list archive.zip

# 详细信息
CompressTool-CLI.exe list --detailed archive.zip
```

## 🔧 系统要求

- **操作系统**: Windows 10/11 (64位)
- **内存**: 建议 4GB 以上
- **磁盘空间**: 200MB 可用空间
- **权限**: 普通用户权限即可

## ⚠️ 注意事项

1. **首次运行**: Windows Defender 可能会进行安全检查，这是正常现象
2. **RAR支持**: RAR格式只支持解压，不支持压缩（专有格式限制）
3. **7Z支持**: 需要额外的py7zr库，在独立版本中可能不可用
4. **大文件**: 处理大文件时请耐心等待，程序会显示进度
5. **路径**: 避免使用包含特殊字符的文件路径

## 🐛 已知问题

1. 某些RAR文件可能无法解压（需要rarfile库）
2. 7Z格式在独立版本中支持有限
3. 非常大的文件（>4GB）可能需要更长处理时间

## 🔄 更新计划

- 改进RAR和7Z格式支持
- 添加更多压缩格式
- 性能优化
- 界面改进

## 📞 技术支持

### 常见问题解决
1. **程序无法启动**
   - 检查Windows版本兼容性
   - 确认杀毒软件未阻止程序运行

2. **压缩/解压失败**
   - 检查文件路径是否正确
   - 确认有足够的磁盘空间
   - 检查文件是否被其他程序占用

3. **密码错误**
   - 确认密码输入正确
   - 注意密码区分大小写

### 获取帮助
- 查看 `使用说明.txt` 获取详细文档
- 运行 `CompressTool-CLI.exe --help` 查看命令帮助
- 使用 `快速开始.bat` 进行交互式操作

## 📄 许可证

本软件遵循 MIT 许可证，可自由使用和分发。

## 🙏 致谢

感谢所有测试用户的反馈和建议！

---

**下载地址**: [发布页面链接]
**发布日期**: 2025-09-10
**版本**: 1.0.0
