#!/usr/bin/env python3
"""
压缩解压工具完整演示
"""

import os
import sys
import subprocess

def run_command(cmd):
    """运行命令并显示结果"""
    print(f"\n> {cmd}")
    print("-" * 50)
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, encoding='utf-8')
    if result.stdout:
        print(result.stdout)
    if result.stderr:
        print("错误:", result.stderr)
    return result.returncode == 0

def main():
    """主演示函数"""
    print("=" * 60)
    print("压缩解压工具完整功能演示")
    print("=" * 60)
    
    # 1. 显示支持的格式
    print("\n1. 显示支持的格式")
    run_command("python main.py formats")
    
    # 2. 压缩单个文件
    print("\n2. 压缩单个文件 (ZIP)")
    run_command("python main.py compress test_data/sample1.txt demo_single.zip -v")
    
    # 3. 压缩多个文件
    print("\n3. 压缩多个文件 (ZIP)")
    run_command("python main.py compress test_data/sample1.txt test_data/sample2.txt demo_multi.zip -v")
    
    # 4. 压缩目录
    print("\n4. 压缩整个目录 (TAR.GZ)")
    run_command("python main.py compress test_data demo_dir.tar.gz -v")
    
    # 5. 查看压缩文件内容
    print("\n5. 查看压缩文件内容")
    run_command("python main.py list demo_multi.zip --detailed")
    
    # 6. 解压文件
    print("\n6. 解压文件")
    run_command("python main.py extract demo_multi.zip demo_extracted -v")
    
    # 7. 不同压缩级别测试
    print("\n7. 不同压缩级别测试")
    run_command("python main.py compress test_data/sample1.txt demo_level0.zip -l 0 -v")
    run_command("python main.py compress test_data/sample1.txt demo_level9.zip -l 9 -v")
    
    # 显示文件大小比较
    print("\n8. 压缩级别效果比较")
    if os.path.exists("demo_level0.zip") and os.path.exists("demo_level9.zip"):
        size0 = os.path.getsize("demo_level0.zip")
        size9 = os.path.getsize("demo_level9.zip")
        print(f"压缩级别 0: {size0:,} 字节")
        print(f"压缩级别 9: {size9:,} 字节")
        print(f"压缩率差异: {((size0-size9)/size0*100):.1f}%")
    
    # 9. Python API演示
    print("\n9. Python API演示")
    try:
        from compress_tool import CompressManager
        
        manager = CompressManager()
        print("✓ 成功创建CompressManager")
        
        # 获取支持格式
        formats = manager.get_supported_formats()
        print(f"✓ 支持 {len(formats['compress'])} 种压缩格式")
        print(f"✓ 支持 {len(formats['extract'])} 种解压格式")
        
        # API压缩测试
        success = manager.compress("test_data/sample1.txt", "demo_api.zip")
        if success:
            print("✓ API压缩成功")
            
            # 获取文件信息
            info = manager.get_archive_info("demo_api.zip")
            print(f"✓ 压缩率: {info['compression_ratio']:.1f}%")
        
    except Exception as e:
        print(f"✗ API演示失败: {e}")
    
    print("\n" + "=" * 60)
    print("演示完成！")
    print("=" * 60)
    
    print("\n生成的文件:")
    demo_files = [f for f in os.listdir('.') if f.startswith('demo_')]
    for file in demo_files:
        if os.path.isfile(file):
            size = os.path.getsize(file)
            print(f"  {file} - {size:,} 字节")
    
    print("\n使用说明:")
    print("1. 命令行使用: python main.py --help")
    print("2. 图形界面: python gui.py")
    print("3. Python API: from compress_tool import CompressManager")
    print("4. 运行测试: python run_tests.py")

if __name__ == '__main__':
    main()
