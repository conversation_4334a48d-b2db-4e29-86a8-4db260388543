# CompressTool 详细使用说明

## 命令行界面详细说明

### 基本语法
CompressTool-CLI.exe [OPTIONS] COMMAND [ARGS]...

### 全局选项
--version    显示版本信息
--help       显示帮助信息

### 命令详解

#### 1. compress - 压缩文件
语法: CompressTool-CLI.exe compress [OPTIONS] SOURCES... OUTPUT

选项:
-l, --level INTEGER    压缩级别 (0-9, 默认6)
-p, --password TEXT    密码保护
-f, --format TEXT      强制指定格式
-v, --verbose          详细输出

示例:
CompressTool-CLI.exe compress file.txt output.zip
CompressTool-CLI.exe compress -l 9 file.txt output.zip
CompressTool-CLI.exe compress -p mypass file.txt output.zip
CompressTool-CLI.exe compress file1.txt file2.txt output.zip
CompressTool-CLI.exe compress folder/ output.tar.gz

#### 2. extract - 解压文件
语法: CompressTool-CLI.exe extract [OPTIONS] ARCHIVE [DESTINATION]

选项:
-p, --password TEXT    密码
-f, --files TEXT       指定解压的文件 (可多次使用)
-v, --verbose          详细输出

示例:
CompressTool-CLI.exe extract archive.zip
CompressTool-CLI.exe extract archive.zip target/
CompressTool-CLI.exe extract -p mypass archive.zip
CompressTool-CLI.exe extract -f file1.txt -f file2.txt archive.zip

#### 3. list - 列出内容
语法: CompressTool-CLI.exe list [OPTIONS] ARCHIVE

选项:
-p, --password TEXT    密码
-d, --detailed         显示详细信息

示例:
CompressTool-CLI.exe list archive.zip
CompressTool-CLI.exe list -d archive.zip
CompressTool-CLI.exe list -p mypass archive.zip

#### 4. formats - 显示支持格式
语法: CompressTool-CLI.exe formats

## 图形界面使用说明

### 主界面
1. 操作选择: 选择压缩、解压或查看内容
2. 文件选择: 选择源文件或压缩文件
3. 目标设置: 设置输出路径
4. 选项配置: 设置压缩级别、密码等
5. 执行操作: 点击按钮开始操作

### 操作步骤
1. 启动程序: 双击 CompressTool-GUI.exe
2. 选择操作类型
3. 点击"浏览"选择文件
4. 设置输出路径
5. 配置选项（可选）
6. 点击执行按钮
7. 查看结果

### 拖拽支持
可以直接将文件拖拽到界面中。

## 高级功能

### 批量操作
命令行版本支持批量处理多个文件：
CompressTool-CLI.exe compress *.txt output.zip

### 压缩级别
- 0: 不压缩（最快）
- 1-3: 低压缩（较快）
- 4-6: 中等压缩（平衡）
- 7-9: 高压缩（较慢）

### 密码保护
支持为ZIP文件设置密码保护。

### 格式转换
可以通过解压再压缩实现格式转换。

## 故障排除

### 常见问题
1. 程序无法启动
   - 检查Windows版本兼容性
   - 确认杀毒软件未阻止

2. 压缩失败
   - 检查源文件是否存在
   - 确认目标路径可写
   - 检查磁盘空间

3. 解压失败
   - 检查压缩文件是否完整
   - 确认密码正确
   - 检查目标路径权限

4. RAR/7Z不支持
   - 这些格式需要额外库支持
   - 可以解压但不能压缩RAR

### 错误代码
- 0: 成功
- 1: 一般错误
- 2: 文件不存在
- 3: 权限不足

## 性能优化

### 大文件处理
- 使用较低的压缩级别
- 确保足够的磁盘空间
- 避免在网络驱动器上操作

### 内存使用
- 程序会根据文件大小自动调整
- 超大文件可能需要更多内存

## 更新日志

### v1.0.0 (2025-09-10)
- 初始版本发布
- 支持ZIP、TAR、RAR、7Z格式
- 提供CLI和GUI界面
- 完整的功能测试

## 许可证
本软件遵循MIT许可证。

## 联系方式
如有问题或建议，请通过项目页面联系。
