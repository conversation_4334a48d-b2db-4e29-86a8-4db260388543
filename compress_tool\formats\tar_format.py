"""
TAR格式压缩解压实现 (支持tar, tar.gz, tar.bz2, tar.xz)
"""

import tarfile
import os
from pathlib import Path
from typing import List, Optional, Union
from ..base import BaseCompressor, CompressionError


class TarCompressor(BaseCompressor):
    """TAR格式压缩器"""
    
    def __init__(self):
        super().__init__()
        self.supported_extensions = ['.tar', '.tar.gz', '.tgz', '.tar.bz2', '.tbz2', '.tar.xz', '.txz']
        self.compression_modes = {
            '.tar': 'w',
            '.tar.gz': 'w:gz',
            '.tgz': 'w:gz',
            '.tar.bz2': 'w:bz2',
            '.tbz2': 'w:bz2',
            '.tar.xz': 'w:xz',
            '.txz': 'w:xz'
        }
    
    def compress(self, source_paths: Union[str, List[str]], 
                 output_path: str, 
                 compression_level: int = 6,
                 password: Optional[str] = None) -> bool:
        """压缩为TAR格式"""
        try:
            if not self.validate_paths(output_path):
                raise CompressionError("输出路径无效")
            
            if isinstance(source_paths, str):
                source_paths = [source_paths]
            
            # 确定压缩模式
            mode = self._get_compression_mode(output_path)
            if not mode:
                raise CompressionError(f"不支持的TAR格式: {output_path}")
            
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir:  # 只有当目录不为空时才创建
                os.makedirs(output_dir, exist_ok=True)
            
            with tarfile.open(output_path, mode) as tar:
                for source_path in source_paths:
                    source_path = Path(source_path)
                    
                    if not source_path.exists():
                        raise CompressionError(f"源路径不存在: {source_path}")
                    
                    # 添加文件或目录到tar
                    tar.add(source_path, arcname=source_path.name)
            
            if password:
                print("警告: TAR格式不支持密码保护")
            
            return True
            
        except Exception as e:
            raise CompressionError(f"TAR压缩失败: {str(e)}")
    
    def extract(self, archive_path: str, 
                extract_to: str,
                password: Optional[str] = None,
                members: Optional[List[str]] = None) -> bool:
        """解压TAR文件"""
        try:
            if not self.validate_paths(archive_path, extract_to):
                raise CompressionError("路径无效")
            
            if not os.path.exists(archive_path):
                raise CompressionError(f"压缩文件不存在: {archive_path}")
            
            os.makedirs(extract_to, exist_ok=True)
            
            with tarfile.open(archive_path, 'r:*') as tar:
                if members:
                    # 提取指定文件
                    for member_name in members:
                        try:
                            member = tar.getmember(member_name)
                            tar.extract(member, extract_to)
                        except KeyError:
                            print(f"警告: 文件 {member_name} 不存在于压缩包中")
                else:
                    # 提取所有文件
                    tar.extractall(extract_to)
            
            if password:
                print("警告: TAR格式不支持密码保护")
            
            return True
            
        except Exception as e:
            raise CompressionError(f"TAR解压失败: {str(e)}")
    
    def list_contents(self, archive_path: str, 
                     password: Optional[str] = None) -> List[dict]:
        """列出TAR文件内容"""
        try:
            if not os.path.exists(archive_path):
                raise CompressionError(f"压缩文件不存在: {archive_path}")
            
            contents = []
            with tarfile.open(archive_path, 'r:*') as tar:
                for member in tar.getmembers():
                    contents.append({
                        'filename': member.name,
                        'file_size': member.size,
                        'compress_size': member.size,  # TAR不压缩，只打包
                        'date_time': member.mtime,
                        'is_dir': member.isdir(),
                        'mode': oct(member.mode),
                        'uid': member.uid,
                        'gid': member.gid
                    })
            
            return contents
            
        except Exception as e:
            raise CompressionError(f"读取TAR文件内容失败: {str(e)}")
    
    def _get_compression_mode(self, output_path: str) -> Optional[str]:
        """根据文件扩展名确定压缩模式"""
        output_path = output_path.lower()
        
        for ext, mode in self.compression_modes.items():
            if output_path.endswith(ext):
                return mode
        
        return None
