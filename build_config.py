#!/usr/bin/env python3
"""
PyInstaller打包配置和构建脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

# 项目信息
PROJECT_NAME = "CompressTool"
VERSION = "1.0.0"
AUTHOR = "Compress Tool"
DESCRIPTION = "压缩解压工具 - 支持ZIP、RAR、TAR、7Z等格式"

# 构建配置
BUILD_DIR = "build"
DIST_DIR = "dist"
SPEC_DIR = "specs"

def clean_build():
    """清理构建目录"""
    print("清理构建目录...")
    for dir_name in [BUILD_DIR, DIST_DIR, SPEC_DIR]:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"  删除 {dir_name}/")
    
    # 清理__pycache__
    for root, dirs, files in os.walk('.'):
        for dir_name in dirs:
            if dir_name == '__pycache__':
                pycache_path = os.path.join(root, dir_name)
                shutil.rmtree(pycache_path)
                print(f"  删除 {pycache_path}")

def create_directories():
    """创建必要的目录"""
    os.makedirs(SPEC_DIR, exist_ok=True)
    os.makedirs(DIST_DIR, exist_ok=True)

def build_cli():
    """构建命令行版本"""
    print("\n构建命令行版本...")
    
    cmd = [
        "pyinstaller",
        "--onefile",                    # 单文件模式
        "--console",                    # 控制台应用
        "--name", f"{PROJECT_NAME}-CLI",
        "--distpath", DIST_DIR,
        "--workpath", BUILD_DIR,
        "--specpath", SPEC_DIR,
        "--add-data", "compress_tool;compress_tool",  # 包含源代码
        "--hidden-import", "compress_tool.formats.zip_format",
        "--hidden-import", "compress_tool.formats.tar_format", 
        "--hidden-import", "compress_tool.formats.rar_format",
        "--hidden-import", "compress_tool.formats.seven_zip",
        "--hidden-import", "zipfile",
        "--hidden-import", "tarfile",
        "--hidden-import", "gzip",
        "--hidden-import", "bz2",
        "--hidden-import", "lzma",
        "main.py"
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ CLI版本构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ CLI版本构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def build_gui():
    """构建GUI版本"""
    print("\n构建GUI版本...")
    
    cmd = [
        "pyinstaller",
        "--onefile",                    # 单文件模式
        "--windowed",                   # 窗口应用（无控制台）
        "--name", f"{PROJECT_NAME}-GUI",
        "--distpath", DIST_DIR,
        "--workpath", BUILD_DIR,
        "--specpath", SPEC_DIR,
        "--add-data", "compress_tool;compress_tool",  # 包含源代码
        "--hidden-import", "compress_tool.formats.zip_format",
        "--hidden-import", "compress_tool.formats.tar_format",
        "--hidden-import", "compress_tool.formats.rar_format", 
        "--hidden-import", "compress_tool.formats.seven_zip",
        "--hidden-import", "tkinter",
        "--hidden-import", "tkinter.ttk",
        "--hidden-import", "tkinter.filedialog",
        "--hidden-import", "tkinter.messagebox",
        "--hidden-import", "tkinter.simpledialog",
        "--hidden-import", "threading",
        "gui.py"
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ GUI版本构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ GUI版本构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def create_portable_version():
    """创建便携版本"""
    print("\n创建便携版本...")
    
    portable_dir = os.path.join(DIST_DIR, f"{PROJECT_NAME}-Portable")
    os.makedirs(portable_dir, exist_ok=True)
    
    # 复制可执行文件
    cli_exe = os.path.join(DIST_DIR, f"{PROJECT_NAME}-CLI.exe")
    gui_exe = os.path.join(DIST_DIR, f"{PROJECT_NAME}-GUI.exe")
    
    if os.path.exists(cli_exe):
        shutil.copy2(cli_exe, portable_dir)
        print(f"  复制 {cli_exe}")
    
    if os.path.exists(gui_exe):
        shutil.copy2(gui_exe, portable_dir)
        print(f"  复制 {gui_exe}")
    
    # 创建示例数据
    sample_dir = os.path.join(portable_dir, "samples")
    os.makedirs(sample_dir, exist_ok=True)
    
    # 复制示例文件
    if os.path.exists("test_data"):
        shutil.copytree("test_data", os.path.join(sample_dir, "test_data"))
        print("  复制示例数据")
    
    # 创建使用说明
    create_usage_guide(portable_dir)
    
    print(f"✓ 便携版本创建完成: {portable_dir}")

def create_usage_guide(target_dir):
    """创建使用说明"""
    usage_content = f"""# {PROJECT_NAME} 使用说明

## 版本信息
- 版本: {VERSION}
- 作者: {AUTHOR}
- 描述: {DESCRIPTION}

## 文件说明
- {PROJECT_NAME}-CLI.exe: 命令行版本
- {PROJECT_NAME}-GUI.exe: 图形界面版本
- samples/: 示例文件

## 命令行使用方法

### 基本命令
```
{PROJECT_NAME}-CLI.exe --help                    # 显示帮助
{PROJECT_NAME}-CLI.exe formats                   # 显示支持格式
```

### 压缩文件
```
{PROJECT_NAME}-CLI.exe compress file.txt output.zip
{PROJECT_NAME}-CLI.exe compress file1.txt file2.txt output.zip
{PROJECT_NAME}-CLI.exe compress folder/ output.tar.gz
```

### 解压文件
```
{PROJECT_NAME}-CLI.exe extract archive.zip
{PROJECT_NAME}-CLI.exe extract archive.zip target_folder/
```

### 查看内容
```
{PROJECT_NAME}-CLI.exe list archive.zip
{PROJECT_NAME}-CLI.exe list archive.zip --detailed
```

### 高级选项
```
{PROJECT_NAME}-CLI.exe compress -l 9 file.txt output.zip     # 最高压缩
{PROJECT_NAME}-CLI.exe compress -p password file.txt output.zip  # 密码保护
{PROJECT_NAME}-CLI.exe compress -v file.txt output.zip       # 详细输出
```

## 图形界面使用方法

双击 {PROJECT_NAME}-GUI.exe 启动图形界面：

1. 选择操作类型（压缩/解压/查看）
2. 选择源文件或文件夹
3. 选择目标路径
4. 设置选项（压缩级别、密码等）
5. 点击执行按钮

## 支持的格式

### 压缩格式
- ZIP (.zip)
- TAR (.tar)
- TAR.GZ (.tar.gz, .tgz)
- TAR.BZ2 (.tar.bz2, .tbz2)
- TAR.XZ (.tar.xz, .txz)
- 7Z (.7z) *需要py7zr库

### 解压格式
- 所有压缩格式
- RAR (.rar) *需要rarfile库

## 注意事项

1. RAR格式只支持解压，不支持压缩
2. 7Z和RAR格式需要额外的库支持
3. 密码保护功能可能在某些格式下不可用
4. 大文件处理时请耐心等待

## 技术支持

如有问题，请检查：
1. 文件路径是否正确
2. 文件是否被其他程序占用
3. 磁盘空间是否充足
4. 权限是否足够

更多信息请参考项目文档。
"""
    
    usage_file = os.path.join(target_dir, "使用说明.txt")
    with open(usage_file, 'w', encoding='utf-8') as f:
        f.write(usage_content)
    
    print(f"  创建使用说明: {usage_file}")

def show_build_info():
    """显示构建信息"""
    print("\n" + "="*60)
    print("构建完成！")
    print("="*60)
    
    if os.path.exists(DIST_DIR):
        print(f"\n生成的文件位于: {DIST_DIR}/")
        for item in os.listdir(DIST_DIR):
            item_path = os.path.join(DIST_DIR, item)
            if os.path.isfile(item_path):
                size = os.path.getsize(item_path)
                print(f"  {item} - {size:,} 字节")
            else:
                print(f"  {item}/ - 文件夹")
    
    print(f"\n使用方法:")
    print(f"1. 命令行版本: {DIST_DIR}/{PROJECT_NAME}-CLI.exe --help")
    print(f"2. 图形界面版本: 双击 {DIST_DIR}/{PROJECT_NAME}-GUI.exe")
    print(f"3. 便携版本: {DIST_DIR}/{PROJECT_NAME}-Portable/")

def main():
    """主构建函数"""
    print(f"开始构建 {PROJECT_NAME} v{VERSION}")
    print("="*60)
    
    # 清理和准备
    clean_build()
    create_directories()
    
    # 构建
    cli_success = build_cli()
    gui_success = build_gui()
    
    if cli_success or gui_success:
        create_portable_version()
        show_build_info()
    else:
        print("\n构建失败！")
        return False
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
