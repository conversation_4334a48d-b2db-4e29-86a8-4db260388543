"""
压缩解压基础接口定义
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Union
from pathlib import Path
import os


class CompressionError(Exception):
    """压缩相关异常"""
    pass


class BaseCompressor(ABC):
    """压缩器基类"""
    
    def __init__(self):
        self.supported_extensions = []
    
    @abstractmethod
    def compress(self, source_paths: Union[str, List[str]], 
                 output_path: str, 
                 compression_level: int = 6,
                 password: Optional[str] = None) -> bool:
        """
        压缩文件或文件夹
        
        Args:
            source_paths: 源文件或文件夹路径
            output_path: 输出压缩文件路径
            compression_level: 压缩级别 (0-9)
            password: 密码保护 (可选)
            
        Returns:
            bool: 压缩是否成功
        """
        pass
    
    @abstractmethod
    def extract(self, archive_path: str, 
                extract_to: str,
                password: Optional[str] = None,
                members: Optional[List[str]] = None) -> bool:
        """
        解压文件
        
        Args:
            archive_path: 压缩文件路径
            extract_to: 解压目标目录
            password: 密码 (可选)
            members: 指定解压的文件列表 (可选)
            
        Returns:
            bool: 解压是否成功
        """
        pass
    
    @abstractmethod
    def list_contents(self, archive_path: str, 
                     password: Optional[str] = None) -> List[dict]:
        """
        列出压缩文件内容
        
        Args:
            archive_path: 压缩文件路径
            password: 密码 (可选)
            
        Returns:
            List[dict]: 文件信息列表
        """
        pass
    
    def is_supported(self, file_path: str) -> bool:
        """检查文件格式是否支持"""
        ext = Path(file_path).suffix.lower()
        return ext in self.supported_extensions
    
    def validate_paths(self, *paths: str) -> bool:
        """验证路径有效性"""
        for path in paths:
            if not path or not isinstance(path, str):
                return False
        return True
