"""
RAR格式解压实现 (仅支持解压，因为RAR是专有格式)
"""

import os
from pathlib import Path
from typing import List, Optional, Union
from ..base import BaseCompressor, CompressionError

try:
    import rarfile
    RARFILE_AVAILABLE = True
except ImportError:
    RARFILE_AVAILABLE = False


class RarCompressor(BaseCompressor):
    """RAR格式处理器 (仅支持解压)"""
    
    def __init__(self):
        super().__init__()
        self.supported_extensions = ['.rar']
        
        if not RARFILE_AVAILABLE:
            print("警告: rarfile库未安装，RAR支持不可用")
            print("请运行: pip install rarfile")
    
    def compress(self, source_paths: Union[str, List[str]], 
                 output_path: str, 
                 compression_level: int = 6,
                 password: Optional[str] = None) -> bool:
        """RAR压缩 (不支持)"""
        raise CompressionError("RAR格式不支持压缩操作，这是专有格式")
    
    def extract(self, archive_path: str, 
                extract_to: str,
                password: Optional[str] = None,
                members: Optional[List[str]] = None) -> bool:
        """解压RAR文件"""
        if not RARFILE_AVAILABLE:
            raise CompressionError("rarfile库未安装，无法处理RAR文件")
        
        try:
            if not self.validate_paths(archive_path, extract_to):
                raise CompressionError("路径无效")
            
            if not os.path.exists(archive_path):
                raise CompressionError(f"压缩文件不存在: {archive_path}")
            
            os.makedirs(extract_to, exist_ok=True)
            
            with rarfile.RarFile(archive_path) as rf:
                if password:
                    rf.setpassword(password)
                
                if members:
                    # 提取指定文件
                    for member in members:
                        try:
                            rf.extract(member, extract_to)
                        except rarfile.NoRarEntry:
                            print(f"警告: 文件 {member} 不存在于RAR压缩包中")
                else:
                    # 提取所有文件
                    rf.extractall(extract_to)
            
            return True
            
        except rarfile.RarWrongPassword:
            raise CompressionError("RAR文件密码错误")
        except rarfile.RarCRCError:
            raise CompressionError("RAR文件CRC校验失败，文件可能损坏")
        except Exception as e:
            raise CompressionError(f"RAR解压失败: {str(e)}")
    
    def list_contents(self, archive_path: str, 
                     password: Optional[str] = None) -> List[dict]:
        """列出RAR文件内容"""
        if not RARFILE_AVAILABLE:
            raise CompressionError("rarfile库未安装，无法处理RAR文件")
        
        try:
            if not os.path.exists(archive_path):
                raise CompressionError(f"压缩文件不存在: {archive_path}")
            
            contents = []
            with rarfile.RarFile(archive_path) as rf:
                if password:
                    rf.setpassword(password)
                
                for info in rf.infolist():
                    contents.append({
                        'filename': info.filename,
                        'file_size': info.file_size,
                        'compress_size': info.compress_size,
                        'date_time': info.date_time,
                        'is_dir': info.is_dir(),
                        'crc': info.CRC,
                        'host_os': info.host_os,
                        'compress_type': info.compress_type
                    })
            
            return contents
            
        except rarfile.RarWrongPassword:
            raise CompressionError("RAR文件密码错误")
        except Exception as e:
            raise CompressionError(f"读取RAR文件内容失败: {str(e)}")
    
    def test_archive(self, archive_path: str, password: Optional[str] = None) -> bool:
        """测试RAR文件完整性"""
        if not RARFILE_AVAILABLE:
            raise CompressionError("rarfile库未安装，无法处理RAR文件")
        
        try:
            with rarfile.RarFile(archive_path) as rf:
                if password:
                    rf.setpassword(password)
                
                # 测试所有文件
                bad_files = rf.testrar()
                return len(bad_files) == 0
                
        except Exception as e:
            raise CompressionError(f"RAR文件测试失败: {str(e)}")
