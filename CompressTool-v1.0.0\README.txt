# CompressTool v1.0.0

## 简介
CompressTool 是一个功能强大的压缩解压工具，支持ZIP、RAR、TAR、7Z等主流压缩格式。

## 版本信息
- 版本: 1.0.0
- 发布日期: 2025-09-10
- 支持系统: Windows 10/11

## 文件说明
- `CompressTool-CLI.exe` - 命令行版本
- `CompressTool-GUI.exe` - 图形界面版本
- `samples/` - 示例文件
- `使用说明.txt` - 详细使用说明
- `快速开始.bat` - 快速开始脚本

## 快速开始

### 图形界面版本
双击 `CompressTool-GUI.exe` 启动图形界面。

### 命令行版本
打开命令提示符，运行：
```
CompressTool-CLI.exe --help
```

## 支持的格式

### 压缩格式
- ZIP (.zip)
- TAR (.tar, .tar.gz, .tar.bz2, .tar.xz)
- 7Z (.7z) *需要额外库支持

### 解压格式
- 所有压缩格式
- RAR (.rar) *需要额外库支持

## 常用命令

### 压缩文件
```
CompressTool-CLI.exe compress file.txt output.zip
CompressTool-CLI.exe compress folder/ output.tar.gz
```

### 解压文件
```
CompressTool-CLI.exe extract archive.zip
CompressTool-CLI.exe extract archive.zip target_folder/
```

### 查看内容
```
CompressTool-CLI.exe list archive.zip
CompressTool-CLI.exe list archive.zip --detailed
```

## 注意事项
1. 首次运行可能需要Windows Defender确认
2. 大文件处理时请耐心等待
3. RAR和7Z格式需要额外的库支持

## 技术支持
如有问题，请检查：
- 文件路径是否正确
- 文件是否被占用
- 磁盘空间是否充足

更多信息请参考详细使用说明。
