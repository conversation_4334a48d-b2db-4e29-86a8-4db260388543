"""
压缩解压功能测试
"""

import unittest
import os
import tempfile
import shutil
from pathlib import Path
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from compress_tool.core import CompressManager
from compress_tool.base import CompressionError


class TestCompressManager(unittest.TestCase):
    """压缩管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.manager = CompressManager()
        self.test_dir = tempfile.mkdtemp()
        self.test_files = []
        
        # 创建测试文件
        for i in range(3):
            file_path = os.path.join(self.test_dir, f"test_file_{i}.txt")
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"这是测试文件 {i}\n" * 100)
            self.test_files.append(file_path)
        
        # 创建测试子目录
        self.test_subdir = os.path.join(self.test_dir, "subdir")
        os.makedirs(self.test_subdir)
        
        subfile_path = os.path.join(self.test_subdir, "subfile.txt")
        with open(subfile_path, 'w', encoding='utf-8') as f:
            f.write("子目录文件内容\n" * 50)
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def test_supported_formats(self):
        """测试支持的格式"""
        formats = self.manager.get_supported_formats()
        
        # 检查基本格式支持
        self.assertIn('.zip', formats['compress'])
        self.assertIn('.tar', formats['compress'])
        self.assertIn('.tar.gz', formats['compress'])
        
        self.assertIn('.zip', formats['extract'])
        self.assertIn('.rar', formats['extract'])
        self.assertIn('.7z', formats['extract'])
    
    def test_format_detection(self):
        """测试格式检测"""
        self.assertEqual(self.manager.detect_format('test.zip'), '.zip')
        self.assertEqual(self.manager.detect_format('test.tar.gz'), '.tar.gz')
        self.assertEqual(self.manager.detect_format('test.rar'), '.rar')
        self.assertEqual(self.manager.detect_format('test.7z'), '.7z')
        self.assertIsNone(self.manager.detect_format('test.unknown'))
    
    def test_zip_compress_extract(self):
        """测试ZIP压缩和解压"""
        zip_path = os.path.join(self.test_dir, "test.zip")
        extract_dir = os.path.join(self.test_dir, "extracted")
        
        # 压缩
        success = self.manager.compress(
            source_paths=self.test_files[0],
            output_path=zip_path
        )
        self.assertTrue(success)
        self.assertTrue(os.path.exists(zip_path))
        
        # 解压
        success = self.manager.extract(
            archive_path=zip_path,
            extract_to=extract_dir
        )
        self.assertTrue(success)
        self.assertTrue(os.path.exists(os.path.join(extract_dir, "test_file_0.txt")))
        
        # 验证内容
        with open(os.path.join(extract_dir, "test_file_0.txt"), 'r', encoding='utf-8') as f:
            content = f.read()
            self.assertIn("这是测试文件 0", content)
    
    def test_zip_multiple_files(self):
        """测试ZIP压缩多个文件"""
        zip_path = os.path.join(self.test_dir, "multiple.zip")
        extract_dir = os.path.join(self.test_dir, "extracted_multiple")
        
        # 压缩多个文件
        success = self.manager.compress(
            source_paths=self.test_files,
            output_path=zip_path
        )
        self.assertTrue(success)
        
        # 解压
        success = self.manager.extract(
            archive_path=zip_path,
            extract_to=extract_dir
        )
        self.assertTrue(success)
        
        # 验证所有文件都被解压
        for i in range(3):
            self.assertTrue(os.path.exists(os.path.join(extract_dir, f"test_file_{i}.txt")))
    
    def test_zip_directory(self):
        """测试ZIP压缩目录"""
        zip_path = os.path.join(self.test_dir, "directory.zip")
        extract_dir = os.path.join(self.test_dir, "extracted_dir")
        
        # 压缩目录
        success = self.manager.compress(
            source_paths=self.test_subdir,
            output_path=zip_path
        )
        self.assertTrue(success)
        
        # 解压
        success = self.manager.extract(
            archive_path=zip_path,
            extract_to=extract_dir
        )
        self.assertTrue(success)
        
        # 验证子目录文件
        self.assertTrue(os.path.exists(os.path.join(extract_dir, "subdir", "subfile.txt")))
    
    def test_tar_compress_extract(self):
        """测试TAR压缩和解压"""
        tar_path = os.path.join(self.test_dir, "test.tar")
        extract_dir = os.path.join(self.test_dir, "extracted_tar")
        
        # 压缩
        success = self.manager.compress(
            source_paths=self.test_files[0],
            output_path=tar_path
        )
        self.assertTrue(success)
        self.assertTrue(os.path.exists(tar_path))
        
        # 解压
        success = self.manager.extract(
            archive_path=tar_path,
            extract_to=extract_dir
        )
        self.assertTrue(success)
        self.assertTrue(os.path.exists(os.path.join(extract_dir, "test_file_0.txt")))
    
    def test_tar_gz_compress_extract(self):
        """测试TAR.GZ压缩和解压"""
        tar_gz_path = os.path.join(self.test_dir, "test.tar.gz")
        extract_dir = os.path.join(self.test_dir, "extracted_tar_gz")
        
        # 压缩
        success = self.manager.compress(
            source_paths=self.test_files[0],
            output_path=tar_gz_path
        )
        self.assertTrue(success)
        self.assertTrue(os.path.exists(tar_gz_path))
        
        # 解压
        success = self.manager.extract(
            archive_path=tar_gz_path,
            extract_to=extract_dir
        )
        self.assertTrue(success)
        self.assertTrue(os.path.exists(os.path.join(extract_dir, "test_file_0.txt")))
    
    def test_list_contents(self):
        """测试列出压缩文件内容"""
        zip_path = os.path.join(self.test_dir, "list_test.zip")
        
        # 压缩多个文件
        success = self.manager.compress(
            source_paths=self.test_files[:2],
            output_path=zip_path
        )
        self.assertTrue(success)
        
        # 列出内容
        contents = self.manager.list_contents(zip_path)
        self.assertEqual(len(contents), 2)
        
        filenames = [item['filename'] for item in contents]
        self.assertIn('test_file_0.txt', filenames)
        self.assertIn('test_file_1.txt', filenames)
    
    def test_archive_info(self):
        """测试获取压缩文件信息"""
        zip_path = os.path.join(self.test_dir, "info_test.zip")
        
        # 压缩文件
        success = self.manager.compress(
            source_paths=self.test_files[0],
            output_path=zip_path
        )
        self.assertTrue(success)
        
        # 获取信息
        info = self.manager.get_archive_info(zip_path)
        
        self.assertEqual(info['format'], '.zip')
        self.assertEqual(info['total_files'], 1)
        self.assertEqual(info['total_directories'], 0)
        self.assertGreater(info['file_size'], 0)
        self.assertGreater(info['uncompressed_size'], 0)
    
    def test_compression_levels(self):
        """测试不同压缩级别"""
        base_path = os.path.join(self.test_dir, "level_test")
        
        sizes = []
        for level in [0, 5, 9]:
            zip_path = f"{base_path}_{level}.zip"
            success = self.manager.compress(
                source_paths=self.test_files[0],
                output_path=zip_path,
                compression_level=level
            )
            self.assertTrue(success)
            sizes.append(os.path.getsize(zip_path))
        
        # 通常压缩级别越高，文件越小（但不是绝对的）
        self.assertGreater(sizes[0], 0)  # 至少文件存在
    
    def test_error_handling(self):
        """测试错误处理"""
        # 不存在的源文件
        with self.assertRaises(CompressionError):
            self.manager.compress(
                source_paths="nonexistent.txt",
                output_path=os.path.join(self.test_dir, "error.zip")
            )
        
        # 不存在的压缩文件
        with self.assertRaises(CompressionError):
            self.manager.extract(
                archive_path="nonexistent.zip",
                extract_to=self.test_dir
            )
        
        # 不支持的格式
        with self.assertRaises(CompressionError):
            self.manager.compress(
                source_paths=self.test_files[0],
                output_path=os.path.join(self.test_dir, "test.unknown")
            )


if __name__ == '__main__':
    unittest.main()
