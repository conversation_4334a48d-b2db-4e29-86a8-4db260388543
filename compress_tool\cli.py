"""
命令行接口
"""

import click
import os
import sys
from pathlib import Path
from typing import List, Optional
from .core import CompressManager
from .base import CompressionError

try:
    from tqdm import tqdm
    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False
    # 简单的进度条替代
    class tqdm:
        def __init__(self, desc="", unit=""):
            self.desc = desc
            print(f"{desc}...")

        def __enter__(self):
            return self

        def __exit__(self, *args):
            pass

        def update(self, n):
            pass


@click.group()
@click.version_option(version='1.0.0')
def cli():
    """压缩解压工具 - 支持ZIP、RAR、TAR、7Z等格式"""
    pass


@cli.command()
@click.argument('sources', nargs=-1, required=True)
@click.argument('output', nargs=1, required=True)
@click.option('--level', '-l', default=6, type=click.IntRange(0, 9), 
              help='压缩级别 (0-9, 默认6)')
@click.option('--password', '-p', help='密码保护')
@click.option('--format', '-f', help='强制指定格式 (zip, tar, tar.gz, 7z等)')
@click.option('--verbose', '-v', is_flag=True, help='详细输出')
def compress(sources: tuple, output: str, level: int, password: Optional[str], 
             format: Optional[str], verbose: bool):
    """压缩文件或文件夹"""
    try:
        manager = CompressManager()
        
        # 验证源文件
        source_list = list(sources)
        for source in source_list:
            if not os.path.exists(source):
                click.echo(f"错误: 源文件不存在: {source}", err=True)
                sys.exit(1)
        
        if verbose:
            click.echo(f"压缩源: {', '.join(source_list)}")
            click.echo(f"输出文件: {output}")
            click.echo(f"压缩级别: {level}")
            if password:
                click.echo("使用密码保护")
        
        # 显示进度条
        with tqdm(desc="压缩中", unit="files") as pbar:
            success = manager.compress(
                source_paths=source_list,
                output_path=output,
                compression_level=level,
                password=password,
                format_type=format
            )
            pbar.update(1)
        
        if success:
            file_size = os.path.getsize(output)
            click.echo(f"✓ 压缩完成: {output} ({file_size:,} 字节)")
        else:
            click.echo("✗ 压缩失败", err=True)
            sys.exit(1)
            
    except CompressionError as e:
        click.echo(f"压缩错误: {e}", err=True)
        sys.exit(1)
    except Exception as e:
        click.echo(f"未知错误: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('archive', required=True)
@click.argument('destination', required=False)
@click.option('--password', '-p', help='密码')
@click.option('--files', '-f', multiple=True, help='指定解压的文件')
@click.option('--verbose', '-v', is_flag=True, help='详细输出')
def extract(archive: str, destination: Optional[str], password: Optional[str], 
            files: tuple, verbose: bool):
    """解压文件"""
    try:
        if not os.path.exists(archive):
            click.echo(f"错误: 压缩文件不存在: {archive}", err=True)
            sys.exit(1)
        
        # 默认解压到当前目录
        if not destination:
            destination = os.path.splitext(os.path.basename(archive))[0]
        
        manager = CompressManager()
        
        if verbose:
            click.echo(f"压缩文件: {archive}")
            click.echo(f"解压目录: {destination}")
            if files:
                click.echo(f"指定文件: {', '.join(files)}")
        
        # 显示进度条
        with tqdm(desc="解压中", unit="files") as pbar:
            success = manager.extract(
                archive_path=archive,
                extract_to=destination,
                password=password,
                members=list(files) if files else None
            )
            pbar.update(1)
        
        if success:
            click.echo(f"✓ 解压完成: {destination}")
        else:
            click.echo("✗ 解压失败", err=True)
            sys.exit(1)
            
    except CompressionError as e:
        click.echo(f"解压错误: {e}", err=True)
        sys.exit(1)
    except Exception as e:
        click.echo(f"未知错误: {e}", err=True)
        sys.exit(1)


@cli.command(name='list')
@click.argument('archive', required=True)
@click.option('--password', '-p', help='密码')
@click.option('--detailed', '-d', is_flag=True, help='显示详细信息')
def list_contents(archive: str, password: Optional[str], detailed: bool):
    """列出压缩文件内容"""
    try:
        if not os.path.exists(archive):
            click.echo(f"错误: 压缩文件不存在: {archive}", err=True)
            sys.exit(1)
        
        manager = CompressManager()
        
        if detailed:
            # 显示详细信息
            info = manager.get_archive_info(archive, password)
            click.echo(f"压缩文件: {info['archive_path']}")
            click.echo(f"格式: {info['format']}")
            click.echo(f"文件大小: {info['file_size']:,} 字节")
            click.echo(f"文件数量: {info['total_files']}")
            click.echo(f"目录数量: {info['total_directories']}")
            click.echo(f"原始大小: {info['uncompressed_size']:,} 字节")
            click.echo(f"压缩大小: {info['compressed_size']:,} 字节")
            click.echo(f"压缩率: {info['compression_ratio']:.1f}%")
            click.echo("\n文件列表:")
            
            contents = info['contents']
        else:
            contents = manager.list_contents(archive, password)
        
        # 显示文件列表
        for item in contents:
            if detailed:
                size_info = f" ({item['file_size']:,} 字节)"
                dir_marker = "/" if item.get('is_dir', False) else ""
                click.echo(f"  {item['filename']}{dir_marker}{size_info}")
            else:
                dir_marker = "/" if item.get('is_dir', False) else ""
                click.echo(f"  {item['filename']}{dir_marker}")
                
    except CompressionError as e:
        click.echo(f"列表错误: {e}", err=True)
        sys.exit(1)
    except Exception as e:
        click.echo(f"未知错误: {e}", err=True)
        sys.exit(1)


@cli.command()
def formats():
    """显示支持的格式"""
    manager = CompressManager()
    supported = manager.get_supported_formats()
    
    click.echo("支持的压缩格式:")
    for fmt in sorted(supported['compress']):
        click.echo(f"  {fmt}")
    
    click.echo("\n支持的解压格式:")
    for fmt in sorted(supported['extract']):
        click.echo(f"  {fmt}")


if __name__ == '__main__':
    cli()
