#!/usr/bin/env python3
"""
压缩解压工具使用示例
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from compress_tool import CompressManager
from compress_tool.base import CompressionError


def create_sample_files():
    """创建示例文件"""
    temp_dir = tempfile.mkdtemp()
    print(f"创建示例文件在: {temp_dir}")
    
    # 创建几个测试文件
    files = []
    for i in range(3):
        file_path = os.path.join(temp_dir, f"sample_{i}.txt")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(f"这是示例文件 {i}\n" * 50)
            f.write(f"文件大小测试内容 {'*' * 100}\n" * 10)
        files.append(file_path)
        print(f"  创建文件: {file_path}")
    
    # 创建子目录
    subdir = os.path.join(temp_dir, "subdir")
    os.makedirs(subdir)
    
    subfile = os.path.join(subdir, "subfile.txt")
    with open(subfile, 'w', encoding='utf-8') as f:
        f.write("子目录中的文件内容\n" * 30)
    files.append(subfile)
    print(f"  创建子目录文件: {subfile}")
    
    return temp_dir, files


def demonstrate_compression():
    """演示压缩功能"""
    print("\n=== 压缩功能演示 ===")
    
    # 创建示例文件
    temp_dir, files = create_sample_files()
    manager = CompressManager()
    
    try:
        # 1. ZIP压缩单个文件
        print("\n1. ZIP压缩单个文件")
        zip_path = os.path.join(temp_dir, "single_file.zip")
        success = manager.compress(
            source_paths=files[0],
            output_path=zip_path,
            compression_level=6
        )
        if success:
            size = os.path.getsize(zip_path)
            print(f"  ✓ 压缩成功: {zip_path} ({size:,} 字节)")
        
        # 2. ZIP压缩多个文件
        print("\n2. ZIP压缩多个文件")
        multi_zip_path = os.path.join(temp_dir, "multiple_files.zip")
        success = manager.compress(
            source_paths=files[:3],
            output_path=multi_zip_path,
            compression_level=9
        )
        if success:
            size = os.path.getsize(multi_zip_path)
            print(f"  ✓ 压缩成功: {multi_zip_path} ({size:,} 字节)")
        
        # 3. TAR.GZ压缩目录
        print("\n3. TAR.GZ压缩整个目录")
        tar_path = os.path.join(temp_dir, "directory.tar.gz")
        success = manager.compress(
            source_paths=temp_dir,
            output_path=tar_path
        )
        if success:
            size = os.path.getsize(tar_path)
            print(f"  ✓ 压缩成功: {tar_path} ({size:,} 字节)")
        
        # 4. 密码保护压缩 (ZIP)
        print("\n4. 密码保护压缩")
        protected_zip = os.path.join(temp_dir, "protected.zip")
        try:
            success = manager.compress(
                source_paths=files[0],
                output_path=protected_zip,
                password="test123"
            )
            if success:
                print(f"  ✓ 密码保护压缩成功: {protected_zip}")
            else:
                print("  ⚠ 密码保护可能不被支持")
        except CompressionError as e:
            print(f"  ⚠ 密码保护压缩: {e}")
        
        return temp_dir, [zip_path, multi_zip_path, tar_path]
        
    except CompressionError as e:
        print(f"  ✗ 压缩失败: {e}")
        return temp_dir, []


def demonstrate_extraction(temp_dir, archives):
    """演示解压功能"""
    print("\n=== 解压功能演示 ===")
    
    manager = CompressManager()
    
    for i, archive_path in enumerate(archives):
        if not os.path.exists(archive_path):
            continue
            
        print(f"\n{i+1}. 解压 {os.path.basename(archive_path)}")
        
        extract_dir = os.path.join(temp_dir, f"extracted_{i}")
        
        try:
            success = manager.extract(
                archive_path=archive_path,
                extract_to=extract_dir
            )
            
            if success:
                print(f"  ✓ 解压成功到: {extract_dir}")
                
                # 列出解压的文件
                for root, dirs, files in os.walk(extract_dir):
                    for file in files:
                        rel_path = os.path.relpath(os.path.join(root, file), extract_dir)
                        print(f"    - {rel_path}")
            else:
                print(f"  ✗ 解压失败")
                
        except CompressionError as e:
            print(f"  ✗ 解压失败: {e}")


def demonstrate_listing(temp_dir, archives):
    """演示文件列表功能"""
    print("\n=== 文件列表功能演示 ===")
    
    manager = CompressManager()
    
    for i, archive_path in enumerate(archives):
        if not os.path.exists(archive_path):
            continue
            
        print(f"\n{i+1}. 查看 {os.path.basename(archive_path)} 内容")
        
        try:
            # 获取详细信息
            info = manager.get_archive_info(archive_path)
            
            print(f"  格式: {info['format']}")
            print(f"  文件大小: {info['file_size']:,} 字节")
            print(f"  文件数量: {info['total_files']}")
            print(f"  目录数量: {info['total_directories']}")
            print(f"  原始大小: {info['uncompressed_size']:,} 字节")
            print(f"  压缩大小: {info['compressed_size']:,} 字节")
            print(f"  压缩率: {info['compression_ratio']:.1f}%")
            
            print("  文件列表:")
            for item in info['contents'][:5]:  # 只显示前5个
                size_info = f" ({item['file_size']:,} 字节)" if not item.get('is_dir', False) else ""
                dir_marker = "/" if item.get('is_dir', False) else ""
                print(f"    {item['filename']}{dir_marker}{size_info}")
            
            if len(info['contents']) > 5:
                print(f"    ... 还有 {len(info['contents']) - 5} 个文件")
                
        except CompressionError as e:
            print(f"  ✗ 读取失败: {e}")


def demonstrate_formats():
    """演示支持的格式"""
    print("\n=== 支持的格式 ===")
    
    manager = CompressManager()
    formats = manager.get_supported_formats()
    
    print("支持压缩的格式:")
    for fmt in sorted(formats['compress']):
        print(f"  {fmt}")
    
    print("\n支持解压的格式:")
    for fmt in sorted(formats['extract']):
        print(f"  {fmt}")


def main():
    """主函数"""
    print("压缩解压工具功能演示")
    print("=" * 50)
    
    try:
        # 演示支持的格式
        demonstrate_formats()
        
        # 演示压缩功能
        temp_dir, archives = demonstrate_compression()
        
        # 演示解压功能
        if archives:
            demonstrate_extraction(temp_dir, archives)
            
            # 演示文件列表功能
            demonstrate_listing(temp_dir, archives)
        
        print(f"\n演示完成！临时文件位于: {temp_dir}")
        print("您可以手动检查生成的文件。")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
