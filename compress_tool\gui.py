"""
图形用户界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import os
import threading
from pathlib import Path
from typing import List, Optional
from .core import CompressManager
from .base import CompressionError


class CompressGUI:
    """压缩解压图形界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("压缩解压工具")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        self.manager = CompressManager()
        self.setup_ui()
        
        # 支持拖拽
        self.root.drop_target_register('DND_Files')
        self.root.dnd_bind('<<Drop>>', self.on_drop)
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="压缩解压工具", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 操作选择
        operation_frame = ttk.LabelFrame(main_frame, text="操作选择", padding="10")
        operation_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.operation_var = tk.StringVar(value="compress")
        ttk.Radiobutton(operation_frame, text="压缩", variable=self.operation_var, 
                       value="compress", command=self.on_operation_change).grid(row=0, column=0, padx=(0, 20))
        ttk.Radiobutton(operation_frame, text="解压", variable=self.operation_var, 
                       value="extract", command=self.on_operation_change).grid(row=0, column=1, padx=(0, 20))
        ttk.Radiobutton(operation_frame, text="查看内容", variable=self.operation_var, 
                       value="list", command=self.on_operation_change).grid(row=0, column=2)
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="10")
        file_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        # 源文件/压缩文件
        ttk.Label(file_frame, text="源文件:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.source_var = tk.StringVar()
        self.source_entry = ttk.Entry(file_frame, textvariable=self.source_var, width=50)
        self.source_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 5), pady=(0, 5))
        self.source_button = ttk.Button(file_frame, text="浏览", command=self.browse_source)
        self.source_button.grid(row=0, column=2, pady=(0, 5))
        
        # 目标文件/目录
        ttk.Label(file_frame, text="目标:").grid(row=1, column=0, sticky=tk.W)
        self.target_var = tk.StringVar()
        self.target_entry = ttk.Entry(file_frame, textvariable=self.target_var, width=50)
        self.target_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 5))
        self.target_button = ttk.Button(file_frame, text="浏览", command=self.browse_target)
        self.target_button.grid(row=1, column=2)
        
        # 选项区域
        options_frame = ttk.LabelFrame(main_frame, text="选项", padding="10")
        options_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 压缩级别
        ttk.Label(options_frame, text="压缩级别:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.level_var = tk.IntVar(value=6)
        level_scale = ttk.Scale(options_frame, from_=0, to=9, variable=self.level_var, 
                               orient=tk.HORIZONTAL, length=200)
        level_scale.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.level_label = ttk.Label(options_frame, text="6")
        self.level_label.grid(row=0, column=2)
        level_scale.configure(command=self.update_level_label)
        
        # 密码
        ttk.Label(options_frame, text="密码:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.password_var = tk.StringVar()
        password_entry = ttk.Entry(options_frame, textvariable=self.password_var, show="*", width=30)
        password_entry.grid(row=1, column=1, sticky=tk.W, pady=(10, 0))
        
        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=(10, 0))
        
        self.execute_button = ttk.Button(button_frame, text="开始压缩", 
                                        command=self.execute_operation, style='Accent.TButton')
        self.execute_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="清空", command=self.clear_fields).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="退出", command=self.root.quit).pack(side=tk.LEFT)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, 
                                          mode='indeterminate')
        self.progress_bar.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(20, 10))
        
        # 状态标签
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var)
        status_label.grid(row=6, column=0, columnspan=3, pady=(0, 10))
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="结果", padding="10")
        result_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(7, weight=1)
        
        # 文本框和滚动条
        self.result_text = tk.Text(result_frame, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 初始化界面状态
        self.on_operation_change()
    
    def on_operation_change(self):
        """操作类型改变时的处理"""
        operation = self.operation_var.get()
        
        if operation == "compress":
            self.execute_button.configure(text="开始压缩")
            self.source_button.configure(text="选择文件/文件夹")
            self.target_button.configure(text="保存为")
        elif operation == "extract":
            self.execute_button.configure(text="开始解压")
            self.source_button.configure(text="选择压缩文件")
            self.target_button.configure(text="解压到")
        else:  # list
            self.execute_button.configure(text="查看内容")
            self.source_button.configure(text="选择压缩文件")
            self.target_entry.configure(state='disabled')
            self.target_button.configure(state='disabled')
            return
        
        self.target_entry.configure(state='normal')
        self.target_button.configure(state='normal')
    
    def browse_source(self):
        """浏览源文件"""
        operation = self.operation_var.get()
        
        if operation == "compress":
            # 可以选择文件或文件夹
            result = filedialog.askopenfilenames(title="选择要压缩的文件")
            if result:
                self.source_var.set(';'.join(result))
        else:
            # 选择压缩文件
            filetypes = [
                ("所有支持的格式", "*.zip;*.rar;*.tar;*.tar.gz;*.tar.bz2;*.tar.xz;*.7z"),
                ("ZIP文件", "*.zip"),
                ("RAR文件", "*.rar"),
                ("TAR文件", "*.tar;*.tar.gz;*.tar.bz2;*.tar.xz"),
                ("7Z文件", "*.7z"),
                ("所有文件", "*.*")
            ]
            result = filedialog.askopenfilename(title="选择压缩文件", filetypes=filetypes)
            if result:
                self.source_var.set(result)
    
    def browse_target(self):
        """浏览目标路径"""
        operation = self.operation_var.get()
        
        if operation == "compress":
            # 保存压缩文件
            filetypes = [
                ("ZIP文件", "*.zip"),
                ("TAR文件", "*.tar"),
                ("TAR.GZ文件", "*.tar.gz"),
                ("7Z文件", "*.7z"),
                ("所有文件", "*.*")
            ]
            result = filedialog.asksaveasfilename(title="保存压缩文件", filetypes=filetypes)
            if result:
                self.target_var.set(result)
        else:
            # 选择解压目录
            result = filedialog.askdirectory(title="选择解压目录")
            if result:
                self.target_var.set(result)
    
    def update_level_label(self, value):
        """更新压缩级别标签"""
        self.level_label.configure(text=str(int(float(value))))
    
    def clear_fields(self):
        """清空所有字段"""
        self.source_var.set("")
        self.target_var.set("")
        self.password_var.set("")
        self.level_var.set(6)
        self.result_text.delete(1.0, tk.END)
        self.status_var.set("就绪")
    
    def execute_operation(self):
        """执行操作"""
        operation = self.operation_var.get()
        
        # 验证输入
        if not self.source_var.get():
            messagebox.showerror("错误", "请选择源文件")
            return
        
        if operation != "list" and not self.target_var.get():
            messagebox.showerror("错误", "请选择目标路径")
            return
        
        # 在新线程中执行操作
        thread = threading.Thread(target=self._execute_operation_thread)
        thread.daemon = True
        thread.start()
    
    def _execute_operation_thread(self):
        """在线程中执行操作"""
        try:
            self.progress_bar.start()
            self.execute_button.configure(state='disabled')
            
            operation = self.operation_var.get()
            source = self.source_var.get()
            target = self.target_var.get()
            password = self.password_var.get() if self.password_var.get() else None
            
            if operation == "compress":
                self.status_var.set("压缩中...")
                sources = source.split(';') if ';' in source else [source]
                success = self.manager.compress(
                    source_paths=sources,
                    output_path=target,
                    compression_level=self.level_var.get(),
                    password=password
                )
                
                if success:
                    size = os.path.getsize(target)
                    self.append_result(f"✓ 压缩完成: {target}\n文件大小: {size:,} 字节\n")
                    self.status_var.set("压缩完成")
                else:
                    self.append_result("✗ 压缩失败\n")
                    self.status_var.set("压缩失败")
            
            elif operation == "extract":
                self.status_var.set("解压中...")
                success = self.manager.extract(
                    archive_path=source,
                    extract_to=target,
                    password=password
                )
                
                if success:
                    self.append_result(f"✓ 解压完成: {target}\n")
                    self.status_var.set("解压完成")
                else:
                    self.append_result("✗ 解压失败\n")
                    self.status_var.set("解压失败")
            
            else:  # list
                self.status_var.set("读取中...")
                info = self.manager.get_archive_info(source, password)
                
                result = f"压缩文件信息:\n"
                result += f"文件: {info['archive_path']}\n"
                result += f"格式: {info['format']}\n"
                result += f"大小: {info['file_size']:,} 字节\n"
                result += f"文件数: {info['total_files']}\n"
                result += f"目录数: {info['total_directories']}\n"
                result += f"原始大小: {info['uncompressed_size']:,} 字节\n"
                result += f"压缩率: {info['compression_ratio']:.1f}%\n\n"
                result += "文件列表:\n"
                
                for item in info['contents']:
                    dir_marker = "/" if item.get('is_dir', False) else ""
                    size_info = f" ({item['file_size']:,} 字节)" if not item.get('is_dir', False) else ""
                    result += f"  {item['filename']}{dir_marker}{size_info}\n"
                
                self.append_result(result)
                self.status_var.set("读取完成")
                
        except CompressionError as e:
            self.append_result(f"错误: {e}\n")
            self.status_var.set("操作失败")
        except Exception as e:
            self.append_result(f"未知错误: {e}\n")
            self.status_var.set("操作失败")
        finally:
            self.progress_bar.stop()
            self.execute_button.configure(state='normal')
    
    def append_result(self, text):
        """添加结果文本"""
        self.result_text.insert(tk.END, text)
        self.result_text.see(tk.END)
    
    def on_drop(self, event):
        """处理拖拽文件"""
        files = event.data.split()
        if files:
            self.source_var.set(';'.join(files))
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()


def main():
    """启动GUI"""
    app = CompressGUI()
    app.run()


if __name__ == '__main__':
    main()
